<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Tutors Alliance Scotland</title>
    <link rel="icon" href="/images/bannerShield2.png" type="image/png">
    <link rel="stylesheet" href="/styles2.css">
    <link rel="stylesheet" href="/css/footer-module.css">
    <link rel="stylesheet" href="/css/button-module.css">
    <link rel="stylesheet" href="/css/typography-module.css">
    <link rel="stylesheet" href="/css/animation-module.css">
    <link rel="stylesheet" href="/header-banner.css">
    <link rel="stylesheet" href="/css/nav.css">
    <link rel="stylesheet" href="/css/admin-forms.css">
    <link rel="stylesheet" href="/css/admin-tables.css">
    <script src="/responsive-helper.js" defer></script>
    <script src="/js/html-sanitizer.js" defer></script>
</head>
<body>
    <!-- ─────────────── HEADER/BANNER ─────────────── -->
    <header>
        <h1 data-ve-block-id="0a085027-3a19-49be-87ae-b06a6e36d043">Tutors Alliance Scotland</h1>
        <div class="header-links">
            <a class="banner-login-link login-box" href="/" data-ve-block-id="a2c3c663-5d9d-4b5b-b630-e017bf8340b4">Home</a>
            <a class="banner-login-link login-box" href="/login.html?role=admin" data-ve-block-id="ee305c05-9d20-4871-927f-7dfc04e21884">Login</a>
        </div>
    </header>

    <!-- Navigation will be loaded here by nav-loader.js -->

    <div class="rolling-banner">
        <div class="rolling-content" id="tutorBanner"></div>
    </div>

    <!-- ─────────────── MAIN CONTENT ─────────────── -->
    <main id="admin-dashboard">
        <!-- Tabs Navigation -->
        <div class="tabs-container">
            <div class="tab-buttons">
                <button class="tab-button active" data-tab="content-tab">Manage Content</button>
                <button class="tab-button" data-tab="tutors-tab">Manage Tutors</button>
            </div>
        </div>

        <!-- Content Tab -->
        <div id="content-tab" class="tab-content active">
            <!-- ░░░░░░  ADD‑/DELETE SECTION  ░░░░░░ -->
            <div class="admin-form-container">
                <!-- Heading will change dynamically -->
                <h3 id="sectionFormHeading" data-ve-block-id="8eee3405-0831-4242-b51f-c00b65269345">Add a Dynamic Section</h3>

                <!-- ★ NEW – simplified form : only page, heading, paragraph, image ★ -->
                <form id="addSection" enctype="multipart/form-data" autocomplete="off">
                    <label>
                        Target Page:
                        <select name="page" id="pageSelect">
                            <!-- Generated dynamically from pages.js -->
                        </select>
                        <a id="viewPageLink" href="/" target="_blank" style="margin-left:12px;font-size:.9em" data-ve-block-id="58511f1e-05ce-4b1b-a7b1-04dd59d69b20">🔗 Open Page</a>
                    </label>

                    <!-- [MOVE-SECTION] ▸ Hidden unless editing -->
                    <label id="movePageRow" style="display:none;margin-top:10px;padding:8px;background:#f0f8ff;border-radius:4px;border-left:3px solid #4a90e2;">
                        <span style="font-weight:500;color:#2c5aa0;">📍 Move this section to:</span>
                        <select name="targetPage" id="movePageSelect" style="margin-left:8px;">
                            <!-- Populated dynamically -->
                        </select>
                        <small style="display:block;margin-top:4px;color:#666;font-style:italic;">
                            Section will be moved to the selected page and admin view will switch automatically.<br>
                            <strong>Note:</strong> You may need to reorder sections on the target page after moving.
                        </small>
                    </label>

                    <!-- ▸▸▸ Rolling-banner ONLY fields (stays visible at all times) -->
                    <div id="rollingBannerFields" style="display:none;">
                        <label>News Content:
                            <textarea name="rollingText" rows="3" required placeholder="Enter news content to display in the rolling banner"></textarea>
                        </label>
                    </div>

                    <!-- ▸▸▸ Meta controls – hidden for banner -->
                    <div id="metaControls">
                        <label>Layout:
                            <select name="layout" id="sectionLayout">
                                <option value="standard" selected>Standard</option>
                                <option value="team">Team Members Grid</option>
                                <option value="list">List Section</option>
                                <option value="testimonial">Testimonial Section</option>
                                <option value="video">Video Section</option>
                            </select>
                        </label>

                        <!-- ★ Navigation integration controls (always visible) -->
                        <label style="display:flex; align-items:center;">
                            <input type="checkbox" name="showInNav" id="showInNav" style="width:auto; margin-right:8px;">
                            <span>Add a link to the navigation bar</span>
                        </label>

                        <label id="navCatRow" style="margin-top:10px; display:none;">
                            Show under:
                            <select name="navCategory" id="sectionNavCategory">
                                <option value="tutors">For Tutors</option>
                                <option value="parents">For Parents</option>
                                <option value="about" selected>About TAS</option>
                            </select>
                        </label>

                        <!-- ★ ONE shared heading field, outside the per-layout wrappers -->
                        <label id="mainHeadingLabel">Heading:<input name="heading" required></label>

                        <div id="standardFields">
                            <!-- ★ Wrapper holds only the controls that belong to the Standard layout -->
                            <div id="standardOnlyFields">
                                <label>Paragraph:<textarea name="text" rows="3" required></textarea></label>
                            </div><!-- /standardOnlyFields -->
                        </div>

                        <!-- ★ SHARED: Image and button fields available for all layout types -->
                        <div id="sharedFields">
                            <label>Image:<input type="file" name="image" id="sectionImage" accept="image/*"></label>
                            <!-- To show current image and allow removal -->
                            <div id="currentImagePreview" style="margin-top: 10px; display: none;">
                                <p style="margin-bottom: 5px;" data-ve-block-id="09d85e4c-83d0-4bb1-836e-f6fb414b5946">Current Image:</p>
                                <img src="" alt="Current Image" style="max-width: 100px; max-height: 100px; border-radius: 4px;" data-ve-block-id="0a1cfcab-196e-4c69-8198-94c82e8f690e">
                                <label style="display: inline-flex; align-items: center; margin-left: 15px;">
                                    <input type="checkbox" name="removeImage" value="true" style="width: auto; margin-right: 5px;"> Remove Image
                                </label>
                            </div>

                            <!-- ★ NEW: Add button fields -->
                            <hr style="border-top: 1px solid #b3a1b3; margin: 20px 0;">
                            <label>Button Label (Optional): <input name="buttonLabel" placeholder="e.g. Learn More"></label>
                            <label>Button URL (Optional): <input name="buttonUrl" type="url" placeholder="https://example.com"></label>

                            <!-- ★ NEW: Checkbox to remove button, shown only during edit -->
                            <label id="removeButtonRow" style="display:none; align-items:center; margin-top: 10px;">
                              <input type="checkbox" name="removeButton" value="true" style="width:auto; margin-right:8px;">
                              Remove Button
                            </label>
                        </div><!-- /sharedFields -->
                    </div><!-- /metaControls -->

                    <!-- ░░░░░░  TEAM BUILDER FIELDS  ░░░░░░ -->
                    <div id="teamBuilder" style="display:none; margin-top:15px;">
                        <h4 style="margin-bottom: 10px; color: #4a90e2;">Team Members</h4>
                        <button type="button" id="addMemberBtn" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-bottom: 15px;">
                            ➕ Add Team Member
                        </button>
                        <div id="teamMemberList"></div>
                        <input type="hidden" name="team" id="teamData">
                    </div>

                    <!-- ░░░░░░  LIST BUILDER FIELDS  ░░░░░░ -->
                    <div id="listBuilder" style="display:none; margin-top:15px;">
                        <h4 style="margin-bottom: 10px; color: #4a90e2;">List Items</h4>
                        <label>List Description (Optional):
                            <textarea id="listDescription" rows="2" placeholder="Brief description of the list..."></textarea>
                        </label>
                        <label>List Type:
                            <select id="listType">
                                <option value="unordered">Bullet Points (•)</option>
                                <option value="ordered">Numbered List (1, 2, 3...)</option>
                            </select>
                        </label>
                        <button type="button" id="addListItemBtn" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin: 10px 0;">
                            ➕ Add List Item
                        </button>
                        <div id="listItemsList"></div>
                        <input type="hidden" name="listItems" id="listItemsData">
                    </div>

                    <!-- ░░░░░░  TESTIMONIAL BUILDER FIELDS  ░░░░░░ -->
                    <div id="testimonialBuilder" style="display:none; margin-top:15px;">
                        <h4 style="margin-bottom: 10px; color: #4a90e2;">Testimonials</h4>
                        <button type="button" id="addTestimonialBtn" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-bottom: 15px;">
                            ➕ Add Testimonial
                        </button>
                        <div id="testimonialList"></div>
                        <input type="hidden" name="testimonials" id="testimonialsData">
                    </div>

                    <!-- ░░░░░░  VIDEO BUILDER FIELDS  ░░░░░░ -->
                    <div id="videoBuilder" style="display:none; margin-top:15px;">
                        <h4 style="margin-bottom: 10px; color: #4a90e2;">Video Section</h4>
                        <label>Video URL:
                            <input type="url" name="videoUrl" id="videoUrl" placeholder="https://example.com/video.mp4">
                        </label>
                        <button type="button" id="browseVideosBtn" style="background: #17a2b8; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 10px;">
                            📹 Browse Available Videos
                        </button>
                        <div id="videoBrowser" style="display: none; margin-top: 15px; padding: 15px; border: 1px solid #ddd; border-radius: 4px; background: #f8f9fa;">
                            <h5>Available Videos</h5>
                            <div id="videoLoadingMsg">Loading videos...</div>
                            <div id="videoCategories" style="display: none;">
                                <div id="staticVideosSection">
                                    <h6>Static Videos</h6>
                                    <div id="staticVideosList"></div>
                                </div>
                                <div id="blobVideosSection">
                                    <h6>Uploaded Videos</h6>
                                    <div id="blobVideosList"></div>
                                </div>
                                <div id="googleCloudVideosSection">
                                    <h6>Google Cloud Videos</h6>
                                    <div id="googleCloudVideosList"></div>
                                </div>
                            </div>
                            <div id="noVideosMsg" style="display: none;">No videos available</div>
                        </div>
                    </div>

                    <!-- Form Action Buttons -->
                    <button type="submit" id="submitSectionBtn">Add Section</button>
                    <button type="button" id="cancelEditBtn" class="cancel-btn" style="display: none;">Cancel Edit</button>
                </form>
            </div>

            <!-- ░░░░░░  EXISTING SECTIONS TABLE  ░░░░░░ -->
            <div class="admin-form-container">
                <h3 style="margin-top: 35px;" data-ve-block-id="cae01088-0f78-4a09-a2dc-0d214075c4a2">Existing sections for this page</h3>
                <table id="sectionTable" class="admin-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Heading</th>
                            <th>Layout</th>
                            <th>Position</th>
                            <th>Image?</th>
                            <th>Button?</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Filled by JavaScript -->
                    </tbody>
                </table>
            </div>

            <!-- ░░░░░░  CREATE NEW PAGE  ░░░░░░ -->
            <div class="admin-form-container">
                <h3 id="pageFormHeading" data-ve-block-id="43a4e36a-6975-46a8-beb1-94b6a0eb2908">Create a New Page</h3>

                <form id="pageForm" enctype="multipart/form-data" autocomplete="off">
                    <label>Page URL (e.g., "summer-tutoring"):
                        <input type="text" name="slug" id="pageSlug" required>
                    </label>

                    <label>Page Title:
                        <input name="heading" required>
                    </label>

                    <label>Page Content:
                        <textarea name="text" rows="6" required></textarea>
                    </label>

                    <label>Show in Navigation Menu:
                        <input type="checkbox" name="showInNav" id="pageShowInNav" style="width: auto; margin-right: 8px;">
                        Add to navigation
                    </label>

                    <label id="pageNavCatRow" style="margin-top: 10px; display: none;">
                        Navigation Category:
                        <select name="navCategory" id="pageNavCategory">
                            <option value="tutors">For Tutors</option>
                            <option value="parents">For Parents</option>
                            <option value="about" selected>About TAS</option>
                        </select>
                    </label>

                    <label>Page Image:
                        <input type="file" name="image" id="pageImage" accept="image/*">
                    </label>

                    <!-- Current Image Preview -->
                    <div id="currentPageImagePreview" style="display: none; margin-top: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; background-color: #f8f9fa;">
                        <p style="margin-bottom: 5px;">Current Image:</p>
                        <img src="" alt="Current Image" style="max-width: 120px; border-radius: 4px;">
                        <label style="display: inline-flex; align-items: center; margin-left: 15px;">
                            <input type="checkbox" name="removeImage" value="true" style="width: auto; margin-right: 5px;"> Remove Image
                        </label>
                    </div>

                    <label>Button Label (Optional):
                        <input name="buttonLabel" placeholder="e.g. Learn More">
                    </label>

                    <label>Button URL (Optional):
                        <input name="buttonUrl" type="url" placeholder="https://example.com">
                    </label>

                    <label>
                        <input type="checkbox" name="isPublished" id="isPublished" style="width: auto; margin-right: 8px;" checked>
                        Publish immediately
                    </label>

                    <!-- Page Edit Options (shown only during edit) -->
                    <div id="pageEditOptions" style="display: none; margin-top: 15px; padding: 10px; background-color: #f0f8ff; border-radius: 4px; border-left: 3px solid #4a90e2;">
                        <h4 style="margin-top: 0;">Page Options</h4>
                        <p style="margin-bottom: 10px; font-size: 0.9em; color: #666;">Additional options available when editing existing pages.</p>
                    </div>

                    <button type="submit" id="submitPageBtn">Create Page</button>
                    <button type="button" id="cancelPageEditBtn" class="cancel-btn" style="display: none;">Cancel Edit</button>
                </form>
            </div>

            <!-- ░░░░░░  EXISTING PAGES TABLE  ░░░░░░ -->
            <div class="admin-form-container">
                <h3 style="margin-top: 35px;" data-ve-block-id="657dfb5e-7981-4780-8bab-bdcd5af52e68">Existing Pages</h3>
                <table id="pagesTable" class="admin-table">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>URL</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Filled by JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Tutors Tab -->
        <div id="tutors-tab" class="tab-content">
            <!-- ░░░░░░  ADD/EDIT TUTOR FORM  ░░░░░░ -->
            <div class="admin-form-container">
                <h3 id="tutorFormHeading" data-ve-block-id="13e6c683-aa50-44ba-92dd-feee30f7c319">Add a Tutor</h3>

                <form id="tutorForm" autocomplete="off">
                    <label>Tutor Name:
                        <input type="text" name="name" required>
                    </label>

                    <label>Subjects (comma-separated):
                        <input type="text" name="subjects" required>
                    </label>

                    <label>Cost Range:
                        <select name="costRange" id="costRangeInput">
                            <option value="£">£15-20 per hour</option>
                            <option value="££">£20-25 per hour</option>
                            <option value="£££">£25-30 per hour</option>
                            <option value="££££">£30-35 per hour</option>
                            <option value="£££££">£35+ per hour</option>
                        </select>
                    </label>

                    <label>Badges (comma-separated):
                        <input type="text" name="badges">
                    </label>

                    <label>Tutor Image:
                        <input type="file" id="imageField" accept="image/*">
                    </label>

                    <label>Description:
                        <textarea name="description" rows="3"></textarea>
                    </label>

                    <label>Contact (email or website):
                        <input type="text" name="contact">
                    </label>

                    <label>Regions:
                        <select name="regions" id="regionsSelect" multiple>
                            <option value="online">Online</option>
                            <option value="aberdeen & aberdeenshire">Aberdeen & Aberdeenshire</option>
                            <option value="angus & dundee">Angus & Dundee</option>
                            <option value="argyll & bute">Argyll & Bute</option>
                            <option value="ayrshire">Ayrshire</option>
                            <option value="borders">Borders</option>
                            <option value="dumfries & galloway">Dumfries & Galloway</option>
                            <option value="edinburgh & lothians">Edinburgh & Lothians</option>
                            <option value="fife">Fife</option>
                            <option value="glasgow & west central">Glasgow & West Central</option>
                            <option value="highlands">Highlands</option>
                            <option value="lanarkshire">Lanarkshire</option>
                            <option value="moray">Moray</option>
                            <option value="perth & kinross">Perth & Kinross</option>
                            <option value="stirling & clackmannanshire">Stirling & Clackmannanshire</option>
                            <option value="western isles">Western Isles</option>
                            <option value="orkney">Orkney</option>
                            <option value="shetland">Shetland</option>
                            <option value="caithness & sutherland">Caithness & Sutherland</option>
                        </select>
                    </label>

                    <!-- Current Image Preview -->
                    <div id="currentTutorImagePreview" style="display: none; margin-top: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; background-color: #f8f9fa;">
                        <p style="margin-bottom: 5px;">Current Image:</p>
                        <img src="" alt="Current Image" style="max-width: 150px; border-radius: 4px; margin-bottom: 10px;">
                        <label style="display: inline-flex; align-items: center; margin-left: 15px;">
                            <input type="checkbox" name="removeImage" value="true" style="width: auto; margin-right: 5px;"> Remove Image
                        </label>
                    </div>

                    <button type="submit" id="submitTutorBtn">Add Tutor</button>
                    <button type="button" id="cancelTutorEditBtn" style="display: none; background-color: #6c757d; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin-left: 10px;">Cancel Edit</button>
                </form>
            </div>

            <!-- ░░░░░░  MANAGE TUTORS  ░░░░░░ -->
            <div class="admin-form-container">
                <h3 data-ve-block-id="39d7ff9d-4901-48f1-86e1-0755df97f9ae">Manage Existing Tutors</h3>
                <p data-ve-block-id="f128aec9-bb79-4cbb-92e2-efce7b8f5367">Click on a tutor's name to view details. Use the delete button to remove a tutor.</p>

                <table id="tutorTable" class="admin-table">
                    <thead>
                        <tr>
                            <th>Image</th>
                            <th>Name</th>
                            <th>Subjects</th>
                            <th>Cost</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Filled by JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <!-- Visual Editor Templates -->
    <template id="ve-editor-modal-template">
        <div id="editor-modal" class="ve-modal-container">
            <div class="modal-backdrop"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modal-title">Edit Content</h3>
                    <button id="close-modal" class="close-btn" aria-label="Close modal">×</button>
                </div>
                <div class="modal-body">
                    <form id="content-form" onsubmit="return false;">
                        <div class="form-group">
                            <label for="content-type">Content Type:</label>
                            <select id="content-type" class="form-control" disabled="">
                                <option value="text">Text</option>
                                <option value="html">HTML</option>
                                <option value="image">Image</option>
                                <option value="link">Link</option>
                            </select>
                        </div>
                        <div class="form-group" id="text-group">
                            <label for="content-text">Text Content:</label>
                            <textarea id="content-text" class="form-control" rows="8" placeholder="Please enter your text content here..."></textarea>

                            <!-- Button Management for Text Elements -->
                            <div class="text-button-management">
                                <h4>Add Button</h4>
                                <p class="help-text">Add a button at the end of this text element</p>

                                <div id="text-buttons-list" class="text-buttons-list">
                                    <!-- Existing buttons will be listed here -->
                                </div>

                                <div class="button-form" id="new-button-form" style="display: none;">
                                    <div class="form-group">
                                        <label for="new-button-text">Button Text:</label>
                                        <input type="text" id="new-button-text" class="form-control" placeholder="Enter button text">
                                    </div>
                                    <div class="form-group">
                                        <label for="new-button-url">Button URL:</label>
                                        <input type="url" id="new-button-url" class="form-control" placeholder="https://example.com">
                                    </div>
                                    <div class="button-form-actions">
                                        <button type="button" id="save-new-button" class="btn btn-primary">Add Button</button>
                                        <button type="button" id="cancel-new-button" class="btn btn-secondary">Cancel</button>
                                    </div>
                                </div>

                                <button type="button" id="add-text-button" class="btn btn-secondary">+ Add Button</button>
                            </div>
                        </div>
                        <div class="form-group" id="html-group">
                            <label for="content-html">HTML Content:</label>
                            <textarea id="content-html" class="form-control" rows="10" placeholder="Enter your HTML content here..."></textarea>
                        </div>
                        <div class="form-group" id="image-group">
                            <label for="content-image">Image URL:</label>
                            <div class="image-input-group">
                                <input type="url" id="content-image" class="form-control" placeholder="Enter image URL or browse/upload below">
                                <button type="button" id="browse-btn" class="btn btn-secondary">Browse Images</button>
                            </div>
                            <div id="image-preview" style="display: none;"><img src="" alt="Preview" style="max-width: 200px; max-height: 200px;"></div>
                            <div class="upload-section">
                                <label for="image-upload">Or upload a new image:</label>
                                <input type="file" id="image-upload" accept="image/*" class="form-control">
                                <button type="button" id="upload-btn" class="btn btn-secondary">Upload Image</button>
                                <div id="upload-progress" style="display: none;">
                                    <div class="progress-bar"><div class="progress-fill"></div></div>
                                    <span class="progress-text">Uploading...</span>
                                </div>
                            </div>
                            <label for="image-alt">Alt Text:</label>
                            <input type="text" id="image-alt" class="form-control" placeholder="Describe the image for accessibility">
                        </div>
                        <div class="form-group" id="link-group">
                            <label for="link-url">Link URL:</label>
                            <input type="url" id="link-url" class="form-control" placeholder="https://example.com">
                            <label for="link-text">Link Text:</label>
                            <input type="text" id="link-text" class="form-control" placeholder="Enter the text to display">
                            <div class="form-check">
                                <input type="checkbox" id="link-is-button" class="form-check-input">
                                <label for="link-is-button" class="form-check-label">Style as button</label>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="button" id="preview-btn" class="btn btn-secondary">Preview</button>
                            <button type="button" id="save-btn" class="btn btn-primary">Save Changes</button>
                            <button type="button" id="restore-btn" class="btn btn-warning">Restore Original</button>
                        </div>
                    </form>
                </div>
            </div>
            <div id="image-browser" class="image-browser" style="display: none;"></div>
        </div>
    </template>

    <template id="ve-image-browser-template">
        <div class="image-browser-header">
            <h4>Browse Images</h4>
            <button type="button" id="close-browser" class="close-btn" aria-label="Close image browser">×</button>
        </div>
        <div class="image-browser-content">
            <div class="image-browser-toolbar">
                <input type="text" id="image-search" placeholder="Search images..." class="form-control">
                <select id="image-sort" class="form-control">
                    <option value="newest">Newest First</option>
                    <option value="oldest">Oldest First</option>
                    <option value="name">Name</option>
                </select>
            </div>
            <div id="image-grid" class="image-grid">
                <div class="loading-spinner"></div>
            </div>
            <div id="image-pagination" class="image-pagination">
                <button type="button" id="prev-page" class="btn btn-secondary" disabled="">Previous</button>
                <span id="page-info">Page 1</span>
                <button type="button" id="next-page" class="btn btn-secondary">Next</button>
            </div>
        </div>
    </template>

    <!-- Scripts -->
    <script src="/js/nav-loader.js" defer></script>
    <script src="/js/dynamic-nav.js" defer></script>
    <script src="/js/rolling-banner.js" defer></script>
    <!-- Google Analytics -->
    <script src="/js/google-analytics.js" defer></script>
    <script type="module" src="/js/dynamic-sections.js"></script>
    <script type="module" src="/js/pages.js"></script>
    <!-- Upload Helper for Image Uploads -->
    <script type="module" src="/js/upload-helper.js"></script>
    <!-- CSRF Helper for secure API requests -->
    <script src="/js/csrf-helper.js"></script>
    <!-- Admin Dashboard Interactive Functionality -->
    <script src="/js/admin-dashboard.js"></script>
    <!-- 🔒 SECURITY: Direct load visual editor for authenticated admin dashboard -->
    <script type="module" src="/js/visual-editor-v2.js?v=20250101-CACHE-BUST&t=1735747800" defer=""></script>
</body>
</html>
