<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Tutor Zone – Tutors Alliance Scotland</title>
    <link rel="icon" href="/images/bannerShield2.png" type="image/png">
    <link rel="stylesheet" href="/styles2.css">
    <link rel="stylesheet" href="/css/footer-module.css">
    <link rel="stylesheet" href="/css/button-module.css">
    <link rel="stylesheet" href="/css/typography-module.css">
    <link rel="stylesheet" href="/css/animation-module.css">
    <link rel="stylesheet" href="/header-banner.css">
    <link rel="stylesheet" href="/css/nav.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Font Awesome for social icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="/responsive-helper.js" defer></script>
    <!-- Google Analytics -->
    <script src="/js/google-analytics.js" defer></script>
</head>
<body class="tutorzone-page" data-page="tutorszone">
    <!-- Shared banner/header -->
    <header>
        <h1 data-ve-block-id="fdbc62d5-669b-4d8f-b468-56f94040b9c6">Tutors Alliance Scotland</h1>
        <div class="header-links">
            <a href="/" class="banner-login-link login-box" data-ve-block-id="898d0856-01dd-4b4e-8953-4341a82f0632">Home</a>
            <a href="login.html?role=admin" class="banner-login-link login-box" data-ve-block-id="18b34fa5-9b45-4c94-81af-da63e9386c9f">Login</a>
        </div>
    </header>

    <!-- Navigation will be loaded here by nav-loader.js -->

    <!-- Rolling banner container -->
    <div class="rolling-banner">
        <div class="rolling-content" id="tutorBanner">
            <!-- JS will populate tutor names/subjects here -->
        </div>
    </div>

    <main>
        <!-- ✦ TOP -->
        <section id="dynamicSectionsTop" class="dynamic-section-container" data-ve-section-id="dynamicSectionsTop"></section>

        <!-- TUTOR RESOURCES ZONE -->
        <section class="tutor-zone-section fade-in-section" data-ve-section-id="tutor-resources">
            <div class="zone-gradient-bg tutor-gradient-bg">
                <div class="zone-list-row">
                    <div class="tutor-box">
                        <h2 data-ve-block-id="ecf10f0c-a69e-4e6e-839d-dc4f2074b30e">📚 Resources for Tutors</h2>
                        <p data-ve-block-id="a4f518ed-88a1-4cb7-96ad-d9ec68d3ffa1">
                            Find guides, tutorials, and other materials to help you excel as a tutor.
                            Our resources are designed to support your professional development and enhance
                            your tutoring practice.
                        </p>
                        <ul class="tutor-list">
                            <li class="no-edit"><a href="#" class="button aurora" data-ve-block-id="466c9384-53ca-4c40-acdb-4fc4f458f2f3">Tutoring Best Practices</a></li>
                            <li class="no-edit"><a href="#" class="button aurora" data-ve-block-id="7aa610ea-1b07-4171-95cb-93ea8dee2112">Professional Development Guides</a></li>
                            <li class="no-edit"><a href="#" class="button aurora" data-ve-block-id="43b7772f-ced1-48e8-8612-cd0b3e627736">Resource Library</a></li>
                            <li class="no-edit"><a href="#" class="button aurora" data-ve-block-id="373e409b-986b-4df9-887f-bafabe68ab6a">View All Resources</a></li>
                        </ul>
                    </div>
                    <img src="/images/tutorStatic1.PNG" alt="Tutoring resources" class="tutor-img-list tutor-img-list-right" data-ve-block-id="995f4a91-2c66-460a-8684-19dd43c571a8">
                </div>
            </div>
        </section>

        <!-- ✦ MIDDLE -->
        <section id="dynamicSectionsMiddle" class="dynamic-section-container" data-ve-section-id="dynamicSectionsMiddle"></section>

        <!-- PROFESSIONAL DEVELOPMENT ZONE -->
        <section class="tutor-zone-section fade-in-section" data-ve-section-id="professional-development">
            <div class="zone-gradient-bg tutor-gradient-bg">
                <div class="zone-list-row">
                    <img src="/images/tutorStatic2.PNG" alt="Professional development" class="tutor-img-list tutor-img-list-left" data-ve-block-id="b8f7c2a1-4d5e-6f7g-8h9i-0j1k2l3m4n5o">
                    <div class="tutor-box">
                        <h2 data-ve-block-id="ac81f63f-233f-40f9-b48c-ef85a13c3ccf">🤝 Connect with Fellow Tutors</h2>
                        <p data-ve-block-id="6fad5ace-a245-4247-9906-9de7ff8260e6">
                            Join our community of professional tutors to share experiences, resources, and best practices.
                            Connect with other tutors in your area or subject specialty.
                        </p>
                        <ul class="tutor-list">
                            <li class="no-edit"><a href="#" class="button aurora" data-ve-block-id="social-facebook">Facebook Group</a></li>
                            <li class="no-edit"><a href="#" class="button aurora" data-ve-block-id="social-linkedin">LinkedIn Network</a></li>
                            <li class="no-edit"><a href="#" class="button aurora" data-ve-block-id="social-discord">Discord Community</a></li>
                            <li class="no-edit"><a href="#" class="button aurora" data-ve-block-id="join-community-btn">Join Community</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- TRAINING & CERTIFICATION -->
        <section class="two-col-content fade-in-section" data-ve-section-id="training-certification">
            <div>
                <h2 data-ve-block-id="training-heading">🎓 Training & Certification</h2>
                <p data-ve-block-id="training-description">
                    Enhance your skills with our comprehensive training programs and earn certifications
                    that demonstrate your expertise to potential students and parents.
                </p>
                <div class="training-grid">
                    <div class="training-card">
                        <h3 data-ve-block-id="cert-basic">Basic Tutoring Certification</h3>
                        <p data-ve-block-id="cert-basic-desc">Foundation skills for new tutors</p>
                        <a href="#" class="button aurora" data-ve-block-id="cert-basic-btn">Learn More</a>
                    </div>
                    <div class="training-card">
                        <h3 data-ve-block-id="cert-advanced">Advanced Teaching Methods</h3>
                        <p data-ve-block-id="cert-advanced-desc">Specialized techniques for experienced tutors</p>
                        <a href="#" class="button aurora" data-ve-block-id="cert-advanced-btn">Learn More</a>
                    </div>
                    <div class="training-card">
                        <h3 data-ve-block-id="cert-subject">Subject-Specific Training</h3>
                        <p data-ve-block-id="cert-subject-desc">Specialized training for your subject area</p>
                        <a href="#" class="button aurora" data-ve-block-id="cert-subject-btn">Learn More</a>
                    </div>
                </div>
            </div>
            <div>
                <img src="/images/tutorStatic0.PNG" alt="Training and certification" data-ve-block-id="training-image">
            </div>
        </section>

        <!-- CTA BANNER -->
        <section class="cta-banner fade-in-section" data-ve-section-id="cta-banner">
            <div class="cta-content">
                <div class="cta-buttons">
                    <a class="button aurora" href="#" data-ve-block-id="ed8e5109-cc37-43d5-9442-e1dcf7f07ed1">View Resources</a>
                    <a class="button aurora" href="#" data-ve-block-id="faa31872-ecfa-4888-9493-b4acb1160ed7">Upcoming Events</a>
                    <a class="button aurora" href="contact.html" data-ve-block-id="98039ade-8684-4ac4-962b-7c97417fa8a2">Contact Us</a>
                </div>
            </div>
        </section>

        <!-- ✦ BOTTOM -->
        <section id="dynamicSections" class="dynamic-section-container" data-ve-section-id="dynamicSections"></section>
    </main>

    <!-- SOCIAL ICONS FOOTER -->
    <footer class="site-footer fade-in-section">
        <div class="footer-icons">
            <a href="https://www.instagram.com/tutorsalliancescotland/" aria-label="Instagram" data-ve-block-id="cc4c0365-e903-4f74-a123-b9eb051f6d52"><i class="fab fa-instagram"></i></a>
            <a href="https://www.facebook.com/tutorsalliancescotland/" aria-label="Facebook" data-ve-block-id="cc24258d-6d03-4357-8abc-dfdd87f9c392"><i class="fab fa-facebook-f"></i></a>
        </div>
    </footer>

    <!-- STATIC BOTTOM FOOTER -->
    <footer class="static-footer">
        <div class="static-footer-container">
            <div class="static-footer-left">
                <h4 data-ve-block-id="6b4b000d-d76d-4106-9c17-ef0a7cf857bf">Extra Information</h4>
                <ul>
                    <li class="no-edit"><a href="tutoring-standards.html" data-ve-block-id="2de28fd6-a5c4-4f0b-b793-42f5ffd7dad0">The TAS Way: Governance and Guidance</a></li>
                    <li class="no-edit"><a href="parents.html#faq" data-ve-block-id="0731b2a7-9365-4c12-80e7-9d6830e24f93">FAQ's</a></li>
                    <li class="no-edit"><a href="privacy-policy.html" data-ve-block-id="************************************">Privacy Policy</a></li>
                    <li class="no-edit"><a href="safeguarding-policy.html" data-ve-block-id="699b2182-1a80-4e3a-90e8-7f4b270c63ad">Safeguarding Policy</a></li>
                    <li class="no-edit"><a href="terms-and-conditions.html" data-ve-block-id="76e69afc-6427-4b33-952c-5e573237f25d">Terms and Conditions</a></li>
                </ul>
                <div class="static-footer-copyright">
                    <p data-ve-block-id="be58a2f6-83cf-4fdc-9908-ce6aaff902a2">ALL RIGHTS RESERVED © Tutors Alliance Scotland 2025</p>
                </div>
                <div class="static-footer-credits">
                    <p data-ve-block-id="32306cdb-8f03-42e7-970f-2dbb3f138968">Website by <a href="#" target="_blank" data-ve-block-id="b77338ab-95de-4046-bd6b-2f79ffba40f5">Tutors Alliance Scotland</a></p>
                </div>
            </div>
            <div class="static-footer-right">
                <div class="website-url">
                    <p data-ve-block-id="28739c2d-b4a4-46a5-bef4-b758c4600585">www.tutorsalliancescotland.co.uk</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Visual Editor Templates -->
    <template id="ve-editor-modal-template">
        <div id="editor-modal" class="ve-modal-container">
            <div class="modal-backdrop"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modal-title">Edit Content</h3>
                    <button id="close-modal" class="close-btn" aria-label="Close modal">×</button>
                </div>
                <div class="modal-body">
                    <form id="content-form" onsubmit="return false;">
                        <div class="form-group">
                            <label for="content-type">Content Type:</label>
                            <select id="content-type" class="form-control" disabled="">
                                <option value="text">Text</option>
                                <option value="html">HTML</option>
                                <option value="image">Image</option>
                                <option value="link">Link</option>
                            </select>
                        </div>
                        <div class="form-group" id="text-group">
                            <label for="content-text">Text Content:</label>
                            <textarea id="content-text" class="form-control" rows="8" placeholder="Enter your text content here..."></textarea>

                            <!-- Button Management for Text Elements -->
                            <div class="text-button-management">
                                <h4>Add Button</h4>
                                <p class="help-text">Add an aurora-style button at the end of this text element</p>

                                <div id="text-buttons-list" class="text-buttons-list">
                                    <!-- Existing buttons will be listed here -->
                                </div>

                                <div class="button-form" id="new-button-form" style="display: none;">
                                    <div class="form-group">
                                        <label for="new-button-text">Button Text:</label>
                                        <input type="text" id="new-button-text" class="form-control" placeholder="Enter button text">
                                    </div>
                                    <div class="form-group">
                                        <label for="new-button-url">Button URL:</label>
                                        <input type="url" id="new-button-url" class="form-control" placeholder="https://example.com">
                                    </div>
                                    <div class="button-form-actions">
                                        <button type="button" id="save-new-button" class="btn btn-primary">Add Button</button>
                                        <button type="button" id="cancel-new-button" class="btn btn-secondary">Cancel</button>
                                    </div>
                                </div>

                                <button type="button" id="add-text-button" class="btn btn-secondary">+ Add Button</button>
                            </div>
                        </div>
                        <div class="form-group" id="html-group">
                            <label for="content-html">HTML Content:</label>
                            <textarea id="content-html" class="form-control" rows="10" placeholder="Enter your HTML content here..."></textarea>
                        </div>
                        <div class="form-group" id="image-group">
                            <label for="content-image">Image URL:</label>
                            <div class="image-input-group">
                                <input type="url" id="content-image" class="form-control" placeholder="Enter image URL or browse/upload below">
                                <button type="button" id="browse-btn" class="btn btn-secondary">Browse Images</button>
                            </div>
                            <div id="image-preview" style="display: none;"><img src="" alt="Preview" style="max-width: 200px; max-height: 200px;"></div>
                            <div class="upload-section">
                                <label for="image-upload">Or upload a new image:</label>
                                <input type="file" id="image-upload" accept="image/*" class="form-control">
                                <button type="button" id="upload-btn" class="btn btn-secondary">Upload Image</button>
                                <div id="upload-progress" style="display: none;">
                                    <div class="progress-bar"><div class="progress-fill"></div></div>
                                    <span class="progress-text">Uploading...</span>
                                </div>
                            </div>
                            <label for="image-alt">Alt Text:</label>
                            <input type="text" id="image-alt" class="form-control" placeholder="Describe the image for accessibility">
                        </div>
                        <div class="form-group" id="link-group">
                            <label for="link-url">Link URL:</label>
                            <input type="url" id="link-url" class="form-control" placeholder="https://example.com">
                            <label for="link-text">Link Text:</label>
                            <input type="text" id="link-text" class="form-control" placeholder="Enter the text to display">
                            <div class="form-check">
                                <input type="checkbox" id="link-is-button" class="form-check-input">
                                <label for="link-is-button" class="form-check-label">Style as button</label>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="button" id="preview-btn" class="btn btn-secondary">Preview</button>
                            <button type="button" id="save-btn" class="btn btn-primary">Save Changes</button>
                            <button type="button" id="restore-btn" class="btn btn-warning">Restore Original</button>
                        </div>
                    </form>
                </div>
            </div>
            <div id="image-browser" class="image-browser" style="display: none;"></div>
        </div>
    </template>

    <template id="ve-image-browser-template">
        <div class="image-browser-header">
            <h4>Browse Images</h4>
            <button type="button" id="close-browser" class="close-btn" aria-label="Close image browser">×</button>
        </div>
        <div class="image-browser-content">
            <div class="image-browser-toolbar">
                <input type="text" id="image-search" placeholder="Search images..." class="form-control">
                <select id="image-sort" class="form-control">
                    <option value="newest">Newest First</option>
                    <option value="oldest">Oldest First</option>
                    <option value="name">Name</option>
                </select>
            </div>
            <div id="image-grid" class="image-grid">
                <div class="loading-spinner"></div>
            </div>
            <div id="image-pagination" class="image-pagination">
                <!-- Pagination will be inserted here -->
            </div>
        </div>
    </template>

    <!-- Scripts -->
    <script src="/js/nav-loader.js" defer></script>
    <script src="/js/dynamic-nav.js" defer></script>
    <script src="/js/rolling-banner.js" defer></script>

    <!-- 🔄 UNIVERSAL: Apply content overrides for all visitors -->
    <script src="/js/universal-content-overrides.js" defer></script>

    <!-- 🔒 SECURITY: Load visual editor only for authenticated admins -->
    <script src="/js/visual-editor-bootstrap.js" defer></script>
    <!-- 🔒 SECURITY: Load HTML sanitizer before dynamic sections -->
    <script src="/js/html-sanitizer.js" defer></script>
    <!-- CSRF Helper for secure API requests -->
    <script src="/js/csrf-helper.js"></script>
    <!-- Google Analytics -->
    <script src="/js/google-analytics.js" defer></script>
    <script type="module" src="/js/dynamic-sections.js?v=20240530" defer></script>
    <script type="module" src="/js/pages.js"></script>
</body>
</html>
