/**
 * CSRF Token Helper for TutorScotland Frontend
 * Provides utilities for handling CSRF tokens in AJAX requests
 * 
 * Features:
 * - Automatic CSRF token retrieval from cookies
 * - Token refresh functionality
 * - Automatic header injection for protected requests
 * - Error handling and retry logic
 */

class CSRFHelper {
    constructor() {
        this.tokenCache = null;
        this.refreshPromise = null;
    }

    /**
     * Get CSRF token from cookie
     * @returns {string|null} CSRF token or null if not found
     */
    getTokenFromCookie() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrf-token') {
                return decodeURIComponent(value);
            }
        }
        return null;
    }

    /**
     * Get current CSRF token (from cache or cookie)
     * @returns {string|null} CSRF token
     */
    getCurrentToken() {
        if (this.tokenCache) {
            return this.tokenCache;
        }
        
        const cookieToken = this.getTokenFromCookie();
        if (cookieToken) {
            this.tokenCache = cookieToken;
            return cookieToken;
        }
        
        return null;
    }

    /**
     * Refresh CSRF token from server
     * @returns {Promise<string>} Promise resolving to new CSRF token
     */
    async refreshToken() {
        // Prevent multiple simultaneous refresh requests
        if (this.refreshPromise) {
            return this.refreshPromise;
        }

        this.refreshPromise = (async () => {
            try {
                console.log('🔒 Refreshing CSRF token...');
                
                const response = await fetch('/api/protected?csrf=true', {
                    method: 'GET',
                    credentials: 'include', // Include cookies
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`CSRF token refresh failed: ${response.status}`);
                }

                const data = await response.json();
                const newToken = data.token;
                
                if (!newToken) {
                    throw new Error('No CSRF token in response');
                }

                // Update cache
                this.tokenCache = newToken;
                console.log('✅ CSRF token refreshed successfully');
                
                return newToken;
                
            } catch (error) {
                console.error('❌ CSRF token refresh failed:', error);
                this.tokenCache = null;
                throw error;
            } finally {
                this.refreshPromise = null;
            }
        })();

        return this.refreshPromise;
    }

    /**
     * Ensure we have a valid CSRF token
     * @returns {Promise<string>} Promise resolving to CSRF token
     */
    async ensureToken() {
        let token = this.getCurrentToken();
        
        if (!token) {
            console.log('🔒 No CSRF token found, refreshing...');
            token = await this.refreshToken();
        }
        
        return token;
    }

    /**
     * Add CSRF token to request headers
     * @param {Object} headers - Existing headers object
     * @returns {Promise<Object>} Headers with CSRF token added
     */
    async addCSRFHeader(headers = {}) {
        const token = await this.ensureToken();
        
        if (token) {
            headers['X-CSRF-Token'] = token;
            console.log('🔒 Added CSRF token to request headers');
        } else {
            console.warn('⚠️ No CSRF token available for request');
        }
        
        return headers;
    }

    /**
     * Make a protected AJAX request with CSRF token
     * @param {string} url - Request URL
     * @param {Object} options - Fetch options
     * @returns {Promise<Response>} Fetch response
     */
    async protectedFetch(url, options = {}) {
        const method = (options.method || 'GET').toUpperCase();
        
        // Only add CSRF token for state-changing methods
        if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(method)) {
            options.headers = await this.addCSRFHeader(options.headers || {});
        }
        
        // Ensure credentials are included
        options.credentials = options.credentials || 'include';
        
        try {
            const response = await fetch(url, options);
            
            // If we get a CSRF error, try refreshing token and retry once
            if (response.status === 403) {
                const errorData = await response.clone().json().catch(() => ({}));
                if (errorData.code === 'CSRF_PROTECTION') {
                    console.log('🔒 CSRF error detected, refreshing token and retrying...');
                    
                    // Refresh token and retry
                    await this.refreshToken();
                    options.headers = await this.addCSRFHeader(options.headers || {});
                    
                    return fetch(url, options);
                }
            }
            
            return response;
            
        } catch (error) {
            console.error('❌ Protected fetch failed:', error);
            throw error;
        }
    }

    /**
     * Clear cached token (useful for logout)
     */
    clearToken() {
        this.tokenCache = null;
        console.log('🔒 CSRF token cache cleared');
    }
}

// Create global instance
window.csrfHelper = new CSRFHelper();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CSRFHelper;
}

console.log('🔒 CSRF Helper loaded and ready');
