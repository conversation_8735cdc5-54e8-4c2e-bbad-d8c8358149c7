/**
 * CSRF Protection Middleware for TutorScotland
 * Implements Cross-Site Request Forgery protection using origin validation
 *
 * Security Features:
 * - Origin header validation for state-changing operations
 * - Referer header validation as fallback
 * - Environment-configurable trusted domain whitelist
 * - Development environment support
 * - Comprehensive security logging
 */

const { SecurityLogger } = require('./security-logger');

// Default trusted domains for CSRF protection
const DEFAULT_TRUSTED_ORIGINS = [
    'https://tutor-scotland.vercel.app',
    'https://www.tutor-scotland.vercel.app',
    'https://tutorsalliancescotland.co.uk',
    'https://www.tutorsalliancescotland.co.uk',
    'http://localhost:3000',
    'http://127.0.0.1:3000',
    // ✅ TEMPORARY: Add common development domains
    'http://localhost:8080',
    'http://localhost:5000',
    'http://localhost:4000',
    'http://127.0.0.1:8080',
    'http://127.0.0.1:5000',
    'http://127.0.0.1:4000',
];

// Load additional trusted origins from environment variable
function loadTrustedOrigins() {
    const envOrigins = process.env.CSRF_TRUSTED_ORIGINS;
    if (envOrigins) {
        const additionalOrigins = envOrigins.split(',').map(origin => origin.trim());
        return [...DEFAULT_TRUSTED_ORIGINS, ...additionalOrigins];
    }
    return DEFAULT_TRUSTED_ORIGINS;
}

// Cache trusted origins
const TRUSTED_ORIGINS = loadTrustedOrigins();

/**
 * Check if the request is in a test environment
 * @returns {boolean} True if in test environment
 */
function isTestEnvironment() {
    return process.env.NODE_ENV === 'test' || 
           process.env.JEST_WORKER_ID !== undefined ||
           process.env.VITEST === 'true' ||
           process.env.CI === 'true';
}

/**
 * Extract origin from request headers
 * @param {Object} req - Express request object
 * @returns {string|null} Origin URL or null
 */
function getOrigin(req) {
    return req.headers.origin || req.headers.referer || null;
}

/**
 * Check if origin is in trusted list
 * @param {string} origin - Origin to check
 * @returns {boolean} True if origin is trusted
 */
function isTrustedOrigin(origin) {
    if (!origin) return false;
    
    // Extract base origin from referer if needed
    if (origin.includes('/') && !origin.startsWith('http')) {
        try {
            const url = new URL(origin);
            origin = url.origin;
        } catch (e) {
            return false;
        }
    }
    
    return TRUSTED_ORIGINS.includes(origin);
}

/**
 * CSRF Protection Middleware
 * Validates requests based on Origin/Referer headers
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function csrfProtection(req, res, next) {
    const method = req.method.toUpperCase();
    
    // Skip CSRF protection for safe methods
    if (['GET', 'HEAD', 'OPTIONS'].includes(method)) {
        return next();
    }
    
    // Skip CSRF protection in test environment
    if (isTestEnvironment()) {
        console.log('⚠️ CSRF Protection: Bypassed for test environment');
        return next();
    }
    
    const origin = getOrigin(req);
    const userAgent = req.headers['user-agent'] || 'Unknown';
    const ip = req.ip || req.connection.remoteAddress || 'Unknown';
    
    // Check if origin is trusted
    if (!isTrustedOrigin(origin)) {
        SecurityLogger.csrfViolation(ip, userAgent, origin, method, req.url);
        
        return res.status(403).json({
            error: 'Forbidden',
            message: 'Invalid request origin',
            code: 'CSRF_PROTECTION'
        });
    }
    
    console.log(`✅ CSRF Protection: ${method} request from ${origin} validated`);
    next();
}

/**
 * Check if CSRF protection is required for the request
 * @param {Object} req - Express request object
 * @returns {boolean} True if CSRF protection is required
 */
function requiresCSRFProtection(req) {
    const method = req.method.toUpperCase();
    return !['GET', 'HEAD', 'OPTIONS'].includes(method);
}

/**
 * Get CSRF configuration for debugging
 * @returns {Object} CSRF configuration object
 */
function getCSRFConfig() {
    return {
        trustedOrigins: TRUSTED_ORIGINS,
        isTestEnvironment: isTestEnvironment(),
        totalTrustedOrigins: TRUSTED_ORIGINS.length
    };
}

module.exports = {
    csrfProtection,
    isTrustedOrigin,
    getOrigin,
    requiresCSRFProtection,
    getCSRFConfig,
    TRUSTED_ORIGINS
};
